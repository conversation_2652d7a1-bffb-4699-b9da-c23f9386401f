---
title: Milestone M4 — TypeScript DDD Architecture Migration
description: Convert milestone automation from bash scripts to maintainable TypeScript application using Domain-Driven Design
created: 2024-12-19
updated: 2025-06-16
version: 1.1.0
status: Approved
tags: [milestone, architecture, typescript, ddd]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🏗️">
<strong>Goal:</strong> Transform complex 1,400+ line bash scripts into a professional, testable, and extensible TypeScript system using Domain-Driven Design architecture that supports CLI, API, and future web interfaces.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "18.0.0"
pnpm: "8.15.4"
typescript: "5.0.0"
jest: "29.7.0"
commander: "11.0.0"
express: "4.18.0"
zod: "3.22.0"
winston: "3.10.0"
```

---

## ✅ Success Criteria

### Core Architecture
1. **DDD Structure**: Complete domain/application/infrastructure separation implemented
2. **Type Safety**: 100% TypeScript with strict mode enabled
3. **CLI Compatibility**: Existing `./milestone-guide.sh M1` commands work functionally equivalent (not byte-identical)
4. **Test Coverage**: ≥85% unit test coverage for all use cases and domain logic
5. **Performance**: CLI startup time ≤3 seconds (vs current bash ~0.5s, accounting for Node.js overhead)

### Functional Parity
6. **Pre-Review Workflow**: All analysis, validation, and decision logic preserved
7. **Task Execution**: Complete task management and git workflow functionality
8. **State Management**: JSON state persistence with backward compatibility
9. **Message System**: Human-agent communication system fully functional
10. **Recovery Modes**: All error handling and recovery scenarios working

### Quality & Extensibility
11. **API Foundation**: REST endpoints for milestone operations implemented
12. **Error Handling**: Comprehensive error types with proper logging
13. **Configuration**: Environment-based configuration system
14. **Documentation**: Complete API documentation and architecture guide
15. **Migration Path**: Zero-downtime migration strategy documented

---

## 📦 Master Deliverables Table

| Category | Item | Description | Success Criteria Reference |
|----------|------|-------------|---------------------------|
| **Code Structure** | `packages/milestone-automation/src/domain/` | Domain entities, value objects, services | See Success Criteria #1 (DDD Structure) |
| | `packages/milestone-automation/src/application/` | Use cases and application services | See Success Criteria #1 (DDD Structure) |
| | `packages/milestone-automation/src/infrastructure/` | External adapters and implementations | See Success Criteria #1 (DDD Structure) |
| | `packages/milestone-automation/src/interfaces/` | CLI and API controllers | See Success Criteria #1 (DDD Structure) |
| | `packages/milestone-automation/dist/cli/` | Compiled CLI executables | See Success Criteria #3 (CLI Compatibility) |
| **Executable Scripts** | `milestone-guide.js` | New TypeScript CLI entry point | See Success Criteria #3 (CLI Compatibility) |
| | `milestone-control.js` | New TypeScript control CLI | See Success Criteria #3 (CLI Compatibility) |
| | `packages/milestone-automation/config/` | Environment configuration | See Success Criteria #13 (Configuration) |
| **Documentation** | `docs/tech-specs/architecture/milestone-automation-ddd.mdx` | DDD architecture documentation | See Success Criteria #14 (Documentation) |
| | `docs/tech-specs/api/milestone-automation-api.mdx` | REST API documentation | See Success Criteria #14 (Documentation) |
| | `docs/migration/bash-to-typescript.mdx` | Migration guide | See Success Criteria #15 (Migration Path) |

---

## 🗂 Directory Layout

```
packages/milestone-automation/
├── src/
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── Milestone.ts
│   │   │   ├── Task.ts
│   │   │   ├── ExecutionState.ts
│   │   │   └── WorkLog.ts
│   │   ├── value-objects/
│   │   │   ├── MilestoneId.ts
│   │   │   ├── TaskId.ts
│   │   │   ├── Phase.ts
│   │   │   ├── ConfidenceScore.ts
│   │   │   └── TaskStatus.ts
│   │   ├── services/
│   │   │   ├── AnalysisService.ts
│   │   │   ├── ValidationService.ts
│   │   │   └── DecisionService.ts
│   │   └── repositories/
│   │       ├── MilestoneRepository.ts
│   │       ├── TaskRepository.ts
│   │       └── StateRepository.ts
│   ├── application/
│   │   ├── use-cases/
│   │   │   ├── PreReviewUseCase.ts
│   │   │   ├── ExecuteTaskUseCase.ts
│   │   │   ├── FinalizeUseCase.ts
│   │   │   └── RecoveryUseCase.ts
│   │   ├── services/
│   │   │   └── MilestoneOrchestrator.ts
│   │   └── dto/
│   │       ├── PreReviewRequest.ts
│   │       ├── PreReviewResponse.ts
│   │       └── ExecutionRequest.ts
│   ├── infrastructure/
│   │   ├── repositories/
│   │   │   ├── FileMilestoneRepository.ts
│   │   │   ├── FileTaskRepository.ts
│   │   │   └── JsonStateRepository.ts
│   │   ├── services/
│   │   │   ├── ShellGitService.ts
│   │   │   ├── FileSystemService.ts
│   │   │   ├── SpecLintService.ts
│   │   │   └── LoggingService.ts
│   │   └── adapters/
│   │       ├── StateAdapter.ts
│   │       └── ConfigAdapter.ts
│   ├── interfaces/
│   │   ├── cli/
│   │   │   ├── MilestoneGuideController.ts
│   │   │   ├── MilestoneControlController.ts
│   │   │   └── CommandParser.ts
│   │   └── api/
│   │       ├── routes/
│   │       │   ├── milestones.ts
│   │       │   └── tasks.ts
│   │       └── middleware/
│   │           ├── errorHandler.ts
│   │           └── validation.ts
│   └── shared/
│       ├── errors/
│       │   ├── DomainError.ts
│       │   ├── ValidationError.ts
│       │   └── InfrastructureError.ts
│       ├── types/
│       │   └── common.ts
│       └── utils/
│           ├── logger.ts
│           └── config.ts
├── dist/
│   ├── cli/
│   │   ├── milestone-guide.js
│   │   └── milestone-control.js
│   └── api/
│       └── server.js
├── config/
│   ├── development.json
│   ├── testing.json
│   └── production.json
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── package.json
├── tsconfig.json
├── jest.config.js
└── README.md
```

---

## 🧠 Key Decisions

### Architecture Decisions
1. **Domain-Driven Design**: Chosen for clear separation of concerns and maintainability
2. **TypeScript**: Selected for type safety and better IDE support over JavaScript
3. **Dependency Injection**: Implemented for testability and loose coupling
4. **Repository Pattern**: Used for data access abstraction and testing
5. **Command Pattern**: Applied for CLI operations and undo functionality

### Technology Decisions
6. **Commander.js**: Selected for CLI argument parsing (mature, well-documented)
7. **Express.js**: Chosen for API foundation (lightweight, familiar)
8. **Jest**: Selected for testing framework (TypeScript support, mocking capabilities)
9. **Winston**: Chosen for logging (structured logging, multiple transports)
10. **Zod**: Selected for runtime validation (TypeScript integration)

### Implementation Decisions
11. **Backward Compatibility**: Maintain exact CLI interface for seamless migration
12. **State Persistence**: Keep JSON format for compatibility with existing workflows
13. **Error Handling**: Custom error hierarchy for better debugging and recovery
14. **Configuration**: Environment-based config for different deployment scenarios
15. **Performance**: Optimize for CLI startup time while maintaining functionality

## 📋 Bash Script Analysis & Migration Guide

### Critical Functions to Migrate

#### State Management Functions
```typescript
// From milestone-guide.sh lines 334-351
interface StateUpdateFunction {
  updateState(phase: string, taskNum?: number): void;
  // Updates: current_phase, current_task, last_updated, phase_history[]
  // File: docs/tech-specs/milestones/state/{milestone}/current-state.json
}

// State file structure (existing format to maintain):
interface MilestoneState {
  milestone_id: string;
  current_phase: "not_started" | "analysis_pending" | "instructions_ready" |
                 "execution_started" | "task_execution" | "milestone_done";
  current_task: number;
  total_tasks: number;
  agent_type: string;
  started_at: string; // ISO date
  last_updated: string; // ISO date
  phase_history: Array<{phase: string, task: number, timestamp: string}>;
  paused?: boolean;
  pause_reason?: string;
}
```

#### Human-Agent Communication System
```typescript
// From milestone-control.sh lines 568-628 and milestone-guide.sh lines 354-433
interface MessageSystem {
  checkHumanMessages(): Promise<void>;
  sendMessage(message: string, to: "human" | "agent"): Promise<void>;
  askHumanQuestion(question: string): Promise<void>;
  enhanceMessage(rawMessage: string): string; // Add context and urgency
}

// Message file structure (existing format):
interface MessageFile {
  messages: Array<{
    id: string;
    timestamp: string; // ISO date
    from: "human" | "agent";
    to: "human" | "agent";
    message: string;
    status: "unread" | "read" | "answered";
  }>;
}
```

#### Situational Awareness Logic
```typescript
// From milestone-control.sh lines 195-219
interface SituationalAwareness {
  analyzeCurrentSituation(phase: string, lastUpdated: string, currentTask: number):
    "stuck_long" | "stuck_medium" | "git_conflict" | "analysis_slow" | "normal";

  // Detection patterns:
  // - stuck_long: >45 minutes since last update
  // - stuck_medium: >20 minutes in task_execution phase
  // - git_conflict: git status shows conflicts or merge issues
  // - analysis_slow: >10 minutes in pre_review phase
}
```

#### Git Workflow Management
```typescript
// From milestone-guide.sh lines 873-1072
interface GitWorkflowManager {
  setupTaskBranch(milestoneId: string, taskNumber: number): Promise<void>;
  // Creates: milestone/{milestone-id}/task-{nn} branch structure

  commitTaskWork(taskNumber: number, milestoneId: string): Promise<void>;
  // Commit message format: "feat(task-{n}): Complete task {n} implementation"

  mergeTaskToMilestone(taskNumber: number, milestoneId: string): Promise<void>;
  // Uses: git merge --squash for clean history

  validateGitState(): Promise<GitValidationResult>;
  // Checks: uncommitted changes, current branch, conflicts
}
```

---

### Confidence Scoring Logic
```typescript
// From milestone-guide.sh lines 590-627
interface ConfidenceScoring {
  extractConfidenceScore(analysisFile: string): number;
  // Regex: /confidence.*score.*([0-9]+)/i
  // Threshold: ≥9 for autonomous execution

  evaluateImplementationReadiness(milestone: Milestone): ConfidenceScore;
  // Factors: task clarity, technical feasibility, resource availability
}
```

### Work Log Validation Patterns
```typescript
// From milestone-guide.sh lines 934-999
interface WorkLogValidator {
  validateWorkLogs(taskNumber: number, milestoneId: string): Promise<boolean>;

  // Accepted completion patterns:
  // - "### Task {n}: COMPLETED"
  // - "Task {n} completed"
  // - "Task {n}: done"
  // - "✅ Task {n} finished"
  // - Any task header followed by completion indicator within 5 lines
}
```

## 🤖 CI Pipeline

### Build Pipeline
```yaml
name: Milestone Automation Build
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm run lint
      - run: pnpm run type-check
      - run: pnpm run test:unit
      - run: pnpm run test:integration
      - run: pnpm run build

  compatibility:
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm run build
      - run: ./scripts/test-cli-compatibility.sh
      - run: ./scripts/test-performance-benchmarks.sh

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm audit
      - run: pnpm run security-scan
```

### Quality Gates
- **Code Coverage**: ≥85% required for merge
- **Type Safety**: 0 TypeScript errors allowed
- **Linting**: 0 ESLint errors/warnings allowed
- **Security**: No high/critical vulnerabilities
- **Performance**: CLI startup benchmarks must pass
- **Compatibility**: All bash script compatibility tests must pass

---

## 🔨 Master Task Breakdown Table

| Phase | # | Branch | Task | Implementation Guide | Estimated Time |
|-------|---|--------|------|---------------------|----------------|
| **Phase 1: Foundation & Domain Layer (Week 1)** | 1 | `m4/setup` | Project setup with TypeScript, testing, and build configuration | Setup package.json, tsconfig.json, jest.config.js in packages/milestone-automation/. Configure build scripts and dev dependencies. | 4 hours |
| | 2 | `m4/domain-entities` | Implement core domain entities (Milestone, Task, ExecutionState, WorkLog) | Create entities with business logic only. No external dependencies. Include validation and state transition rules. | 6 hours |
| | 3 | `m4/value-objects` | Create value objects (MilestoneId, Phase, ConfidenceScore, TaskStatus) | Implement immutable value objects with validation. ConfidenceScore.isHighConfidence() returns score ≥ 9. | 4 hours |
| | 4 | `m4/domain-services` | Implement domain services (AnalysisService, ValidationService) | Extract confidence scoring logic from bash script. Implement spec-lint validation patterns. | 8 hours |
| | 5 | `m4/repositories` | Define repository interfaces and contracts | Create interfaces only. No implementations yet. Define contracts for state, milestone, and message persistence. | 3 hours |
| **Phase 2: Application Layer (Week 2)** | 6 | `m4/use-cases-prereview` | Implement PreReviewUseCase with confidence scoring logic | Replicate milestone-guide.sh lines 577-627. Extract confidence from analysis files. Handle autonomous vs human decision paths. | 8 hours |
| | 7 | `m4/use-cases-execution` | Implement ExecuteTaskUseCase with git workflow management | Most complex task. Replicate milestone-guide.sh task execution flow (lines 796-932). Include branch management, work log validation, commit patterns. | 16 hours |
| | 8 | `m4/use-cases-finalization` | Implement FinalizeUseCase with acceptance testing | Replicate milestone-guide.sh finalization phase (lines 1075-1200). Include acceptance test execution and cleanup. | 8 hours |
| | 9 | `m4/use-cases-recovery` | Implement RecoveryUseCase with error handling scenarios | Replicate milestone-guide.sh recovery logic (lines 1300-1350). Handle stuck detection and manual recovery guidance. | 10 hours |
| | 10 | `m4/orchestrator` | Create MilestoneOrchestrator for workflow coordination | Coordinate use cases. Replicate main phase routing logic from milestone-guide.sh lines 175-184. | 4 hours |
| **Phase 3: Infrastructure & CLI (Week 3)** | 11 | `m4/infrastructure-repos` | Implement file-based repositories (JSON state, milestone files) | Implement StateAdapter for legacy compatibility. Use existing JSON formats. Handle file I/O with proper error handling. | 8 hours |
| | 12 | `m4/infrastructure-services` | Implement external services (Git, SpecLint, FileSystem) | Wrap shell commands for git operations. Integrate with existing spec-lint.mjs. Maintain exact command patterns from bash scripts. | 8 hours |
| | 13 | `m4/cli-controllers` | Create CLI controllers with argument parsing and presentation | Use Commander.js to replicate exact CLI interface. Maintain identical help text and error messages. Handle --autonomous flag. | 8 hours |
| | 14 | `m4/api-foundation` | Implement basic REST API with Express.js foundation | Create REST endpoints for milestone operations. Use existing use cases. Provide JSON responses for state and operations. | 6 hours |
| | 15 | `m4/migration-testing` | End-to-end testing and bash script compatibility validation | Critical task. Create comprehensive test suite comparing bash vs TypeScript behavior. Test all 14 acceptance test scenarios. | 12 hours |

---

## 🧪 Master Testing Strategy

### Testing Framework & Coverage
Based on Success Criteria #4 and Technical Requirements:

| Test Type | Framework | Coverage Target | Location | Purpose |
|-----------|-----------|-----------------|----------|---------|
| **Unit Tests** | Jest + ts-jest | ≥85% | `tests/unit/` | Domain logic, value objects |
| **Integration Tests** | Jest | Key workflows | `tests/integration/` | Use case orchestration |
| **E2E Tests** | Jest + CLI | Critical paths | `tests/e2e/` | Full system validation |
| **Performance Tests** | Custom scripts | All benchmarks | CI pipeline | Performance validation |
| **Compatibility Tests** | Bash scripts | 100% parity | `scripts/test-*` | Bash vs TypeScript |

### Test Categories & References
- **CLI Compatibility**: [Acceptance Tests](#acceptance-tests) - Tests 1-3, 7-9
- **State Management**: [Acceptance Tests](#acceptance-tests) - Tests 4-6
- **Performance Benchmarks**: [Master Performance Requirements](#-master-performance-requirements)
- **API Foundation**: [Acceptance Tests](#acceptance-tests) - Tests 13-14
- **Quality Assurance**: [Quality Assurance](#quality-assurance) - Unit/Integration examples
- **CI Pipeline**: [CI Pipeline](#ci-pipeline) - Automated test execution

### Testing Implementation
Referenced in:
- [Master Task Breakdown Table](#-master-task-breakdown-table) - Task #15 (Migration Testing)
- [Implementation Checklist](#implementation-checklist) - TDD approach and test plans
- [Quality Gates](#quality-gates) - Test coverage requirements
- [Definition of Done](#definition-of-done) - Test suite validation

---

## 🧪 Master Acceptance Tests

### Test Categories & Execution
Based on Success Criteria #3 (CLI Compatibility) and Master Testing Strategy:

| Test ID | Category | Purpose | Success Criteria Reference |
|---------|----------|---------|---------------------------|
| **Tests 1-3** | CLI Compatibility | Basic execution, autonomous mode, control operations | Success Criteria #3 |
| **Tests 4-6** | State Management | File format, message system, cross-system transitions | Success Criteria #8 |
| **Tests 7-9** | Behavioral Compatibility | Situational awareness, git workflow, work log validation | Success Criteria #6, #7, #10 |
| **Tests 10-12** | Performance Benchmarks | CLI startup, memory usage, file I/O performance | Success Criteria #5 |
| **Tests 13-14** | API Foundation | REST operations, state synchronization | Success Criteria #11 |

### Test Implementation Scripts
Referenced in:
- [Master Testing Strategy](#-master-testing-strategy) - Compatibility Tests framework
- [Master Performance Requirements](#-master-performance-requirements) - Performance benchmark specifications
- [Master Task Breakdown Table](#-master-task-breakdown-table) - Task #15 (Migration Testing)
- [CI Pipeline](#ci-pipeline) - Automated test execution

### Test Execution Commands
```bash
# CLI Compatibility (Tests 1-3)
./milestone-guide.js M1 augment                    # Test 1: Basic execution
./milestone-guide.js M1 augment --autonomous       # Test 2: Autonomous mode
./milestone-control.js M1 status                   # Test 3: Control operations

# State Management (Tests 4-6) - Cross-system compatibility validation
# Performance Benchmarks (Tests 10-12) - See Master Performance Requirements
# API Foundation (Tests 13-14) - REST endpoint validation
```

### Detailed Test Specifications
For complete test procedures, see:
- **CLI Compatibility**: Bash vs TypeScript functional behavior comparison
- **State Management**: Legacy state file format compatibility validation
- **Behavioral Compatibility**: Situational awareness and workflow matching
- **Performance Benchmarks**: [Master Performance Requirements](#-master-performance-requirements) table
- **API Foundation**: REST endpoint functionality and state synchronization

---

## 🔧 Technical Requirements

### Dependencies
- **Runtime**: Node.js ≥18.0.0
- **Language**: TypeScript ≥5.0.0 with strict mode
- **Testing**: See [Master Testing Strategy](#-master-testing-strategy) for complete testing framework
- **CLI**: Commander.js for argument parsing
- **API**: Express.js for REST endpoints
- **Validation**: Zod for runtime type validation
- **Logging**: Winston for structured logging (see [Master Error Handling Strategy](#-master-error-handling-strategy))

### Architecture Constraints
- **Pure Domain Layer**: No external dependencies in domain entities (supports Success Criteria #1)
- **Dependency Injection**: Use dependency injection for all services (supports Success Criteria #1)
- **Error Handling**: See [Master Error Handling Strategy](#-master-error-handling-strategy) for complete specifications
- **Configuration**: Environment-based config with validation (supports Success Criteria #13)
- **Backward Compatibility**: Existing JSON state files must work (supports Success Criteria #8)

---

## 🚀 Master Performance Requirements

### Core Performance Targets
Based on Success Criteria #5 and Implementation Decision #15:

| Metric | Current (Bash) | Target (TypeScript) | Acceptance Ratio | Rationale |
|--------|----------------|---------------------|------------------|-----------|
| **CLI Startup** | ~0.5s | ≤3s | 6x acceptable | Node.js runtime overhead |
| **Memory Usage** | ~20MB | ≤150MB | 7.5x acceptable | TypeScript runtime + dependencies |
| **File I/O** | Baseline | No regression | 1x | Critical for state management |
| **Git Operations** | Baseline | No regression | 1x | Core workflow dependency |

### Performance Benchmarks & Tests
Referenced in:
- [Acceptance Tests](#acceptance-tests) - Performance Benchmarks section (Tests 10-12)
- [Quality Assurance](#quality-assurance) - Performance Benchmarks section
- [CI Pipeline](#ci-pipeline) - Performance benchmark scripts
- [Quality Gates](#quality-gates) - Performance gate requirements

### Performance Monitoring Strategy
- **Development**: Regular benchmarking during implementation (Implementation Checklist)
- **CI/CD**: Automated performance tests in build pipeline
- **Quality Gates**: Performance benchmarks must pass before merge
- **Mitigation**: Performance monitoring throughout development (Risk Assessment)

---

## 🚨 Master Error Handling Strategy

### Error Type Hierarchy
Based on Success Criteria #12 and Implementation Decision #13:

| Error Category | Error Types | Location | Purpose |
|----------------|-------------|----------|---------|
| **Domain Errors** | `InvalidPhaseTransitionError`, `BusinessRuleViolationError` | `src/shared/errors/DomainError.ts` | Business logic violations |
| **Validation Errors** | `ConfigValidationError`, `InputValidationError` | `src/shared/errors/ValidationError.ts` | Input/config validation |
| **Infrastructure Errors** | `FileSystemError`, `GitOperationError`, `NetworkError` | `src/shared/errors/InfrastructureError.ts` | External system failures |

### Error Handling Patterns
Referenced in:
- [Architecture Constraints](#architecture-constraints) - Error boundaries and custom types
- [Master Task Breakdown Table](#-master-task-breakdown-table) - Task #9 (RecoveryUseCase)
- [Directory Layout](#-directory-layout) - Error handling middleware and types
- [Risk Assessment](#risk-assessment) - Error scenarios and mitigation

### Recovery Scenarios
Based on Success Criteria #10 and bash script recovery logic:

| Scenario | Detection | Recovery Action | Implementation |
|----------|-----------|-----------------|----------------|
| **Stuck Long** | >45 min since update | Human intervention prompt | RecoveryUseCase |
| **Git Conflicts** | Git status conflicts | Guided resolution steps | GitWorkflowManager |
| **State Corruption** | Invalid state file | Rollback to last valid state | StateAdapter |
| **Analysis Timeout** | >10 min in pre-review | Confidence threshold bypass | AnalysisService |

### Logging Strategy
- **Structured Logging**: Winston with JSON format for all error scenarios
- **Error Context**: Include milestone ID, phase, task number in all error logs
- **Recovery Tracking**: Log all recovery attempts and outcomes

---

## 🔄 Master Migration Strategy

### Migration Approach
Based on Success Criteria #15 (Migration Path) and #10 (Recovery Modes):

| Phase | Strategy | Rollback Plan | Validation |
|-------|----------|---------------|------------|
| **Development** | Parallel implementation with bash scripts | Git branch rollback | Unit/integration tests |
| **Testing** | Side-by-side compatibility validation | Revert to bash scripts | Acceptance test comparison |
| **Deployment** | Gradual rollout with feature flags | Instant bash script fallback | Real milestone execution |
| **Production** | Zero-downtime migration with monitoring | Automated rollback triggers | Performance monitoring |

### Migration Components
Referenced in:
- [Migration Compatibility](#-migration-compatibility) - State and CLI compatibility requirements
- [Master Task Breakdown Table](#-master-task-breakdown-table) - Task #15 (Migration Testing)
- [Master Error Handling Strategy](#-master-error-handling-strategy) - Recovery scenarios
- [Definition of Done](#-definition-of-done) - Migration validation requirements

### Rollback Strategy
- **Immediate Rollback**: Feature flag toggle to bash scripts (< 1 minute)
- **State Preservation**: All TypeScript state files compatible with bash scripts
- **Zero Data Loss**: Bidirectional state conversion ensures no information loss
- **Monitoring**: Automated detection of performance regression or errors

### Migration Validation
- **Compatibility Testing**: All 14 acceptance tests must pass
- **Performance Validation**: No regression beyond acceptable thresholds
- **Real Milestone Execution**: Successfully complete actual milestone using TypeScript
- **Rollback Testing**: Verify rollback procedures work under various scenarios

---

### Implementation Examples

See [Appendix: Implementation Examples](#-appendix-implementation-examples) for complete code examples:

- **Domain Layer**: Entity and value object implementations (Appendix A.1)
- **Application Layer**: Use case and service implementations (Appendix A.2)
- **Infrastructure Layer**: Repository and adapter implementations (Appendix A.3)
- **Interface Layer**: CLI controller and API implementations (Appendix A.4)
- **Migration Compatibility**: State and CLI compatibility examples (Appendix A.5)
- **Testing**: Unit, integration, and performance test examples (Appendix A.6)

---

## 🚨 Risk Assessment

### High Risks
- **Complexity**: Large architectural change with many moving parts
- **Compatibility**: Ensuring functional parity with bash scripts (exact byte-for-byte compatibility not required)
- **Performance**: TypeScript overhead vs bash script speed (acceptable 6x slowdown)
- **Bash Logic Migration**: Complex situational awareness and error recovery logic needs careful analysis

### Mitigation Strategies
- **Incremental Migration**: Phase-by-phase implementation with testing
- **Compatibility Testing**: Extensive test suite comparing bash vs TypeScript functional behavior
- **Performance Monitoring**: Benchmark critical operations throughout development
- **Bash Script Analysis**: Comprehensive documentation of all bash script behaviors before migration
- **Prototype Critical Components**: Build proof-of-concept for complex use cases (ExecuteTaskUseCase, RecoveryUseCase)

### Rollback Plan
- **Bash Scripts Preserved**: Keep original scripts as fallback
- **Feature Flags**: Environment variable to switch between implementations
- **State Compatibility**: Ensure state files work with both versions

---

## 📈 Implementation Strategy

### Development Approach
1. **Domain-First**: Start with pure business logic, no external dependencies
2. **Test-Driven**: Write tests before implementation for critical use cases
3. **Incremental**: Build and test each layer before moving to the next
4. **Compatibility-First**: Ensure CLI compatibility at every step

### Quality Gates
- **Code Review**: All code reviewed for DDD principles adherence (supports Success Criteria #1)
- **Test Coverage**: ≥85% coverage required for merge (enforces Success Criteria #4)
- **Performance**: Benchmark tests must pass before merge (enforces Success Criteria #5)
- **Documentation**: Architecture decisions documented in ADRs (supports Success Criteria #14)

---

## 🎯 Definition of Done

- [ ] All 15 tasks completed and merged to main branch (see [Master Task Breakdown Table](#-master-task-breakdown-table))
- [ ] Success Criteria #3: CLI commands have 100% functional parity with bash scripts
- [ ] Success Criteria #4: Test suite passes with ≥85% coverage
- [ ] Success Criteria #5: Performance benchmarks meet requirements
- [ ] Success Criteria #11: API foundation endpoints functional
- [ ] Success Criteria #14: Documentation complete and reviewed
- [ ] Success Criteria #15: Migration guide validated (see [Master Migration Strategy](#-master-migration-strategy))
- [ ] Success Criteria #10: Rollback strategy tested (see [Master Migration Strategy](#-master-migration-strategy))

**Final Validation**: Successfully execute a real milestone (M1 or M2) using the new TypeScript system with identical results to the bash version.

---

## 🛠️ Technical Architecture

### DDD Layer Structure
Based on Success Criteria #1 and [Directory Layout](#-directory-layout):

- **Domain Layer**: Pure business logic with no external dependencies (see Appendix A.1)
- **Application Layer**: Use cases and workflow orchestration (see Appendix A.2)
- **Infrastructure Layer**: External system adapters and implementations (see Appendix A.3)
- **Interface Layer**: CLI controllers and API endpoints (see Appendix A.4)

### Implementation References
For detailed code examples and patterns:
- **Entity Implementations**: [Appendix A.1.1](#a11-milestone-entity) - Milestone entity with business rules
- **Use Case Patterns**: [Appendix A.2.1](#a21-use-case-implementation) - PreReviewUseCase example
- **Repository Patterns**: [Appendix A.3.1](#a31-repository-implementation) - File-based repository implementation
- **CLI Controllers**: [Appendix A.4.1](#a41-cli-controller-implementation) - Bash compatibility patterns

---

## 🔄 Migration Compatibility

### Compatibility Requirements
Based on Success Criteria #8 (State Management) and #3 (CLI Compatibility):

- **State File Compatibility**: Existing JSON state files must work with TypeScript system
- **CLI Argument Compatibility**: Exact same command-line interface as bash scripts
- **Message System Compatibility**: Human-agent communication format preservation
- **Git Workflow Compatibility**: Identical branch and commit patterns

### Implementation Examples
For detailed compatibility patterns, see:
- **State File Compatibility**: [Appendix A.5.1](#a51-state-file-compatibility) - Legacy state conversion
- **CLI Argument Compatibility**: [Appendix A.4.2](#a42-cli-argument-compatibility) - Command parsing patterns

---

## 📊 Success Metrics

### Maintainability Improvements
- **Cyclomatic Complexity**: Reduce from bash script complexity to <10 per function
- **Code Duplication**: Eliminate duplicate logic through proper abstraction
- **Separation of Concerns**: Clear boundaries between domain, application, and infrastructure

### Developer Experience Improvements
- **Type Safety**: 100% TypeScript coverage with strict mode
- **IDE Support**: Full IntelliSense, refactoring, and debugging capabilities
- **Testing**: Unit tests for all business logic, integration tests for workflows
- **Documentation**: Auto-generated API docs, architecture decision records

### Extensibility Improvements
- **New Features**: Add new milestone phases without modifying existing code
- **Multiple Interfaces**: Support CLI, API, and future web interfaces
- **Plugin Architecture**: Support for custom analysis providers and validators
- **Configuration**: Environment-based configuration for different deployment scenarios

---

## 🔍 Quality Assurance

### Testing Framework Reference
See [Master Testing Strategy](#-master-testing-strategy) for complete testing approach and coverage requirements.

### Test Implementation Examples
For detailed test patterns and examples, see [Appendix A.6.1](#a61-test-implementation-examples):
- **Unit Tests**: Domain entity and value object testing patterns
- **Integration Tests**: Use case workflow testing with mocked dependencies
- **End-to-End Tests**: CLI compatibility validation approaches
- **Performance Tests**: Benchmark testing for startup time and memory usage

---

## 🚀 Future Roadmap

### Phase 4: Web Interface (Future)
- React-based dashboard for milestone management
- Real-time progress tracking
- Visual workflow representation
- Team collaboration features

### Phase 5: Advanced Features (Future)
- AI-powered milestone analysis
- Automated dependency detection
- Integration with project management tools
- Advanced reporting and analytics

---

## ✅ Acceptance Criteria Priority

### Must Have (MVP)
- [ ] **CLI Compatibility** (Success Criteria #3)
- [ ] **Type Safety** (Success Criteria #2)
- [ ] **Test Coverage** ≥85% (Success Criteria #4)
- [ ] **Performance** CLI startup ≤3s (Success Criteria #5)
- [ ] **Documentation** Complete (Success Criteria #14)

### Should Have
- [ ] **API Foundation** (Success Criteria #11)
- [ ] **Error Handling** (Success Criteria #12)
- [ ] **Configuration** (Success Criteria #13)
- [ ] **Migration Path** (Success Criteria #15)

### Could Have (Future Enhancements)
- [ ] **Web Dashboard**: Basic web interface for milestone management
- [ ] **Webhook Support**: Integration with external systems
- [ ] **Plugin Architecture**: Support for custom extensions
- [ ] **Advanced Analytics**: Detailed reporting and metrics

---

## 📋 Implementation Checklist

### Pre-Implementation
- [ ] Review existing bash scripts for all functionality (supports Success Criteria #6-10)
- [ ] Document all bash script behaviors, edge cases, and error handling (supports Success Criteria #10, #12)
- [ ] Identify all external dependencies and integrations (supports Success Criteria #1)
- [ ] Set up development environment with TypeScript tooling (supports Success Criteria #2)
- [ ] Create comprehensive test plan (supports Success Criteria #4)
- [ ] Build prototypes for complex use cases (supports Success Criteria #7, #9)

### During Implementation
- [ ] Follow TDD approach for critical business logic (supports Success Criteria #4)
- [ ] Maintain backward compatibility at each step (supports Success Criteria #8)
- [ ] Document architectural decisions in ADRs (supports Success Criteria #14)
- [ ] Regular performance benchmarking (supports Success Criteria #5)
- [ ] Complete tasks in order as defined in [Master Task Breakdown Table](#-master-task-breakdown-table)

### Post-Implementation
- [ ] Comprehensive testing with real milestones (validates Success Criteria #4)
- [ ] Performance validation and optimization (validates Success Criteria #5)
- [ ] Documentation review and updates (validates Success Criteria #14)
- [ ] Migration guide validation (validates Success Criteria #15)

**Ready for Agent Execution**: This milestone is designed to be executed by a software agent using the current `milestone-guide.sh` script, providing a real-world test of the automation system while building its own replacement.

---

## 🔄 Document History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0 | 2024-12-19 | nitishMehrotra | Initial milestone specification with DDD architecture design |
| 1.1.0 | 2025-06-16 | augment | Updated performance requirements and risk assessment based on implementation analysis |
| 1.2.0 | 2025-06-18 | augment | Added comprehensive bash script analysis and implementation guidance to address 6/10 confidence concerns |

### Change Log
- **v1.2.0**: Major specification enhancement to address implementation readiness concerns
  - Added detailed bash script analysis with function mappings and behavior documentation
  - Included comprehensive implementation examples for domain entities, state adapters, and CLI controllers
  - Enhanced task breakdown with specific implementation guidance and bash script line references
  - Added extensive testing strategy with unit, integration, and behavioral compatibility tests
  - Documented critical functions: state management, message system, situational awareness, git workflows
  - Added performance benchmarking details and acceptance test scenarios
  - Included TypeScript code examples for complex migration patterns
- **v1.1.0**: Updated milestone based on implementation feasibility analysis
  - Relaxed CLI startup time from 2s to 3s (accounting for Node.js overhead)
  - Increased memory usage limit from 100MB to 150MB
  - Clarified compatibility requirements (functional parity vs byte-identical)
  - Increased time estimates for complex tasks (ExecuteTaskUseCase, RecoveryUseCase, migration testing)
  - Added bash script analysis and prototyping requirements
  - Updated performance benchmarks to allow 6x slowdown vs bash
- **v1.0.0**: Created comprehensive milestone specification for TypeScript DDD migration
  - Defined 15 tasks across 3 phases for systematic implementation
  - Established architecture with domain/application/infrastructure separation
  - Specified compatibility requirements and performance benchmarks
  - Added comprehensive testing strategy and quality gates
  - Included migration path and rollback strategy

---

## 📚 Appendix: Implementation Examples

### A.1 Domain Layer Examples

#### A.1.1 Milestone Entity
```typescript
// Example: Milestone entity with business logic
export class Milestone {
  constructor(
    private readonly id: MilestoneId,
    private readonly tasks: Task[],
    private phase: Phase,
    private confidenceScore?: ConfidenceScore
  ) {}

  canProceedToExecution(): boolean {
    return this.confidenceScore?.isHighConfidence() ?? false;
  }

  updatePhase(newPhase: Phase): void {
    // Business rule: Can only move forward in phases
    if (!this.phase.canTransitionTo(newPhase)) {
      throw new InvalidPhaseTransitionError(this.phase, newPhase);
    }
    this.phase = newPhase;
  }

  getCurrentTask(): Task | null {
    return this.tasks.find(task => task.isInProgress()) ?? null;
  }
}
```

#### A.1.2 Core Entities Structure
```typescript
// Core Entities
class Milestone {
  constructor(
    private id: MilestoneId,
    private tasks: Task[],
    private phase: Phase,
    private confidenceScore?: ConfidenceScore
  ) {}

  canProceedToExecution(): boolean {
    return this.confidenceScore?.isHighConfidence() ?? false;
  }
}

class Task {
  constructor(
    private id: TaskId,
    private status: TaskStatus,
    private branch: string,
    private workLog?: WorkLog
  ) {}
}

// Value Objects
class ConfidenceScore {
  constructor(private value: number) {
    if (value < 1 || value > 10) {
      throw new Error('Confidence score must be between 1 and 10');
    }
  }

  isHighConfidence(): boolean {
    return this.value >= 9; // Matches bash script threshold
  }
}
```

### A.2 Application Layer Examples

#### A.2.1 Use Case Implementation
```typescript
// Use Cases
class PreReviewUseCase {
  constructor(
    private milestoneRepo: MilestoneRepository,
    private analysisService: AnalysisService,
    private validationService: ValidationService
  ) {}

  async execute(request: PreReviewRequest): Promise<PreReviewResponse> {
    const milestone = await this.milestoneRepo.findById(request.milestoneId);
    const quickAnalysis = await this.analysisService.performQuickAnalysis(milestone);
    const detailedAnalysis = await this.analysisService.performDetailedAnalysis(milestone);

    if (request.isAutonomous) {
      return this.handleAutonomousDecision(quickAnalysis, detailedAnalysis);
    }

    return this.handleHumanDecision(quickAnalysis, detailedAnalysis);
  }
}
```

### A.3 Infrastructure Layer Examples

#### A.3.1 Repository Implementation
```typescript
// Repository Implementations
class FileMilestoneRepository implements MilestoneRepository {
  async findById(id: MilestoneId): Promise<Milestone> {
    const filePath = `docs/tech-specs/milestones/milestone-${id.value}.mdx`;
    const content = await this.fileSystem.readFile(filePath);
    return this.milestoneParser.parse(content);
  }
}

// External Service Adapters
class ShellGitService implements GitService {
  async createBranch(name: string): Promise<void> {
    await this.shell.exec(`git checkout -b ${name}`);
  }
}
```

#### A.3.2 State Adapter Implementation
```typescript
// Example: Legacy state compatibility
export class StateAdapter {
  convertLegacyToNew(legacyState: any): ExecutionState {
    return new ExecutionState(
      new MilestoneId(legacyState.milestone_id),
      Phase.fromString(legacyState.current_phase),
      legacyState.current_task,
      legacyState.total_tasks,
      new Date(legacyState.last_updated),
      legacyState.phase_history?.map(h => new PhaseHistoryEntry(
        Phase.fromString(h.phase),
        h.task,
        new Date(h.timestamp)
      )) ?? []
    );
  }

  convertNewToLegacy(state: ExecutionState): any {
    return {
      milestone_id: state.milestoneId.value,
      current_phase: state.currentPhase.toString(),
      current_task: state.currentTask,
      total_tasks: state.totalTasks,
      last_updated: state.lastUpdated.toISOString(),
      phase_history: state.phaseHistory.map(h => ({
        phase: h.phase.toString(),
        task: h.task,
        timestamp: h.timestamp.toISOString()
      }))
    };
  }
}
```

### A.4 Interface Layer Examples

#### A.4.1 CLI Controller Implementation
```typescript
// Example: Maintaining exact CLI compatibility
export class MilestoneGuideController {
  async run(args: string[]): Promise<void> {
    // Parse arguments exactly like bash script
    const milestoneId = args[0];
    const agentType = args[1] || 'augment';
    const isAutonomous = args.includes('--autonomous');

    if (!milestoneId) {
      this.showUsage();
      process.exit(1);
    }

    // Delegate to use cases with same logic flow as bash
    const request = new PreReviewRequest(
      new MilestoneId(milestoneId),
      agentType,
      isAutonomous
    );

    const response = await this.preReviewUseCase.execute(request);

    // Output formatting matches bash script exactly
    this.formatOutput(response);
  }

  private showUsage(): void {
    console.log(`Usage: ${process.argv[1]} <milestone-id> [agent-type] [--autonomous]`);
    console.log('Example: milestone-guide.js M1.2 augment');
    console.log('Example: milestone-guide.js M1.2 augment --autonomous');
  }
}
```

#### A.4.2 CLI Argument Compatibility
```typescript
// Maintain exact same CLI interface
// ./milestone-guide.js M1 augment --autonomous
class MilestoneGuideController {
  async run(args: string[]): Promise<void> {
    const milestoneId = args[0];
    const agentType = args[1] || 'augment';
    const isAutonomous = args.includes('--autonomous');

    // Delegate to use cases...
  }
}
```

### A.5 Migration Compatibility Examples

#### A.5.1 State File Compatibility
```typescript
// Ensure backward compatibility with existing JSON state files
interface LegacyState {
  milestone_id: string;
  current_phase: string;
  current_task: number;
  total_tasks: number;
  // ... other legacy fields
}

class StateAdapter {
  convertLegacyToNew(legacy: LegacyState): ExecutionState {
    return new ExecutionState(
      new MilestoneId(legacy.milestone_id),
      Phase.fromString(legacy.current_phase),
      legacy.current_task,
      legacy.total_tasks
    );
  }
}
```

### A.6 Testing Examples

#### A.6.1 Test Implementation Examples
```typescript
// Unit Tests
describe('ConfidenceScore', () => {
  it('should identify high confidence correctly', () => {
    const highScore = new ConfidenceScore(9);
    expect(highScore.isHighConfidence()).toBe(true);
  });
});

// Integration Tests
describe('PreReviewUseCase', () => {
  it('should proceed when confidence is high', async () => {
    // Test full workflow with mocked dependencies
  });
});

// End-to-End Tests
describe('CLI Compatibility', () => {
  it('should match bash script behavior', async () => {
    // Compare outputs between bash and TypeScript versions
  });
});

// Performance Tests
describe('Performance', () => {
  it('should start CLI within 3 seconds', async () => {
    const start = Date.now();
    await runCLI(['M1', '--help']);
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(3000);
  });
});
```
