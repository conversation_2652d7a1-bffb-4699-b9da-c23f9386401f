---
title: Milestone M4 — TypeScript DDD Architecture Migration
description: Convert milestone automation from bash scripts to maintainable TypeScript application using Domain-Driven Design
created: 2024-12-19
updated: 2025-06-16
version: 1.1.0
status: Approved
tags: [milestone, architecture, typescript, ddd]
authors: [nitishMehrotra]
---

import { Callout } from '@/components/Callout'

<Callout emoji="🏗️">
<strong>Goal:</strong> Transform complex 1,400+ line bash scripts into a professional, testable, and extensible TypeScript system using Domain-Driven Design architecture that supports CLI, API, and future web interfaces.
</Callout>

---

## 🧳 Toolchain Versions

| Package | Version |
|---------|---------|
| Node.js | 18.0.0 |
| pnpm | 8.15.4 |
| TypeScript | 5.0.0 |
| Jest | 29.7.0 |
| Commander.js | 11.0.0 |
| Express.js | 4.18.0 |
| Zod | 3.22.0 |
| Winston | 3.10.0 |

---

## ✅ Success Criteria

### Core Architecture
1. **DDD Structure**: Complete domain/application/infrastructure separation implemented
2. **Type Safety**: 100% TypeScript with strict mode enabled
3. **CLI Compatibility**: Existing `./milestone-guide.sh M1` commands work functionally equivalent (not byte-identical)
4. **Test Coverage**: ≥85% unit test coverage for all use cases and domain logic
5. **Performance**: CLI startup time ≤3 seconds (vs current bash ~0.5s, accounting for Node.js overhead)

### Functional Parity
6. **Pre-Review Workflow**: All analysis, validation, and decision logic preserved
7. **Task Execution**: Complete task management and git workflow functionality
8. **State Management**: JSON state persistence with backward compatibility
9. **Message System**: Human-agent communication system fully functional
10. **Recovery Modes**: All error handling and recovery scenarios working

### Quality & Extensibility
11. **API Foundation**: REST endpoints for milestone operations implemented
12. **Error Handling**: Comprehensive error types with proper logging
13. **Configuration**: Environment-based configuration system
14. **Documentation**: Complete API documentation and architecture guide
15. **Migration Path**: Zero-downtime migration strategy documented

---

## 📦 Deliverables

### Code Structure
| Directory | Description | Success Criteria |
|-----------|-------------|------------------|
| `packages/milestone-automation/src/domain/` | Domain entities, value objects, services | Pure business logic, no external dependencies |
| `packages/milestone-automation/src/application/` | Use cases and application services | Orchestrates domain logic, handles workflows |
| `packages/milestone-automation/src/infrastructure/` | External adapters and implementations | File system, Git, external tool integrations |
| `packages/milestone-automation/src/interfaces/` | CLI and API controllers | Clean separation of presentation logic |
| `packages/milestone-automation/dist/cli/` | Compiled CLI executables | Drop-in replacement for bash scripts |

### Executable Scripts
| File | Description | Success Criteria |
|------|-------------|------------------|
| `milestone-guide.js` | New TypeScript CLI entry point | Identical command-line interface to bash version |
| `milestone-control.js` | New TypeScript control CLI | All control operations functional |
| `packages/milestone-automation/config/` | Environment configuration | Development, testing, production configs |

### Documentation
| File | Description | Success Criteria |
|------|-------------|------------------|
| `docs/tech-specs/architecture/milestone-automation-ddd.mdx` | DDD architecture documentation | Complete domain model and layer descriptions |
| `docs/tech-specs/api/milestone-automation-api.mdx` | REST API documentation | OpenAPI spec with all endpoints |
| `docs/migration/bash-to-typescript.mdx` | Migration guide | Step-by-step migration instructions |

---

## 🗂 Directory Layout

```
packages/milestone-automation/
├── src/
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── Milestone.ts
│   │   │   ├── Task.ts
│   │   │   ├── ExecutionState.ts
│   │   │   └── WorkLog.ts
│   │   ├── value-objects/
│   │   │   ├── MilestoneId.ts
│   │   │   ├── TaskId.ts
│   │   │   ├── Phase.ts
│   │   │   ├── ConfidenceScore.ts
│   │   │   └── TaskStatus.ts
│   │   ├── services/
│   │   │   ├── AnalysisService.ts
│   │   │   ├── ValidationService.ts
│   │   │   └── DecisionService.ts
│   │   └── repositories/
│   │       ├── MilestoneRepository.ts
│   │       ├── TaskRepository.ts
│   │       └── StateRepository.ts
│   ├── application/
│   │   ├── use-cases/
│   │   │   ├── PreReviewUseCase.ts
│   │   │   ├── ExecuteTaskUseCase.ts
│   │   │   ├── FinalizeUseCase.ts
│   │   │   └── RecoveryUseCase.ts
│   │   ├── services/
│   │   │   └── MilestoneOrchestrator.ts
│   │   └── dto/
│   │       ├── PreReviewRequest.ts
│   │       ├── PreReviewResponse.ts
│   │       └── ExecutionRequest.ts
│   ├── infrastructure/
│   │   ├── repositories/
│   │   │   ├── FileMilestoneRepository.ts
│   │   │   ├── FileTaskRepository.ts
│   │   │   └── JsonStateRepository.ts
│   │   ├── services/
│   │   │   ├── ShellGitService.ts
│   │   │   ├── FileSystemService.ts
│   │   │   ├── SpecLintService.ts
│   │   │   └── LoggingService.ts
│   │   └── adapters/
│   │       ├── StateAdapter.ts
│   │       └── ConfigAdapter.ts
│   ├── interfaces/
│   │   ├── cli/
│   │   │   ├── MilestoneGuideController.ts
│   │   │   ├── MilestoneControlController.ts
│   │   │   └── CommandParser.ts
│   │   └── api/
│   │       ├── routes/
│   │       │   ├── milestones.ts
│   │       │   └── tasks.ts
│   │       └── middleware/
│   │           ├── errorHandler.ts
│   │           └── validation.ts
│   └── shared/
│       ├── errors/
│       │   ├── DomainError.ts
│       │   ├── ValidationError.ts
│       │   └── InfrastructureError.ts
│       ├── types/
│       │   └── common.ts
│       └── utils/
│           ├── logger.ts
│           └── config.ts
├── dist/
│   ├── cli/
│   │   ├── milestone-guide.js
│   │   └── milestone-control.js
│   └── api/
│       └── server.js
├── config/
│   ├── development.json
│   ├── testing.json
│   └── production.json
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── package.json
├── tsconfig.json
├── jest.config.js
└── README.md
```

---

## 🧠 Key Decisions

### Architecture Decisions
1. **Domain-Driven Design**: Chosen for clear separation of concerns and maintainability
2. **TypeScript**: Selected for type safety and better IDE support over JavaScript
3. **Dependency Injection**: Implemented for testability and loose coupling
4. **Repository Pattern**: Used for data access abstraction and testing
5. **Command Pattern**: Applied for CLI operations and undo functionality

### Technology Decisions
6. **Commander.js**: Selected for CLI argument parsing (mature, well-documented)
7. **Express.js**: Chosen for API foundation (lightweight, familiar)
8. **Jest**: Selected for testing framework (TypeScript support, mocking capabilities)
9. **Winston**: Chosen for logging (structured logging, multiple transports)
10. **Zod**: Selected for runtime validation (TypeScript integration)

### Implementation Decisions
11. **Backward Compatibility**: Maintain exact CLI interface for seamless migration
12. **State Persistence**: Keep JSON format for compatibility with existing workflows
13. **Error Handling**: Custom error hierarchy for better debugging and recovery
14. **Configuration**: Environment-based config for different deployment scenarios
15. **Performance**: Optimize for CLI startup time while maintaining functionality

## 📋 Bash Script Analysis & Migration Guide

### Critical Functions to Migrate

#### State Management Functions
```typescript
// From milestone-guide.sh lines 334-351
interface StateUpdateFunction {
  updateState(phase: string, taskNum?: number): void;
  // Updates: current_phase, current_task, last_updated, phase_history[]
  // File: docs/tech-specs/milestones/state/{milestone}/current-state.json
}

// State file structure (existing format to maintain):
interface MilestoneState {
  milestone_id: string;
  current_phase: "not_started" | "analysis_pending" | "instructions_ready" |
                 "execution_started" | "task_execution" | "milestone_done";
  current_task: number;
  total_tasks: number;
  agent_type: string;
  started_at: string; // ISO date
  last_updated: string; // ISO date
  phase_history: Array<{phase: string, task: number, timestamp: string}>;
  paused?: boolean;
  pause_reason?: string;
}
```

#### Human-Agent Communication System
```typescript
// From milestone-control.sh lines 568-628 and milestone-guide.sh lines 354-433
interface MessageSystem {
  checkHumanMessages(): Promise<void>;
  sendMessage(message: string, to: "human" | "agent"): Promise<void>;
  askHumanQuestion(question: string): Promise<void>;
  enhanceMessage(rawMessage: string): string; // Add context and urgency
}

// Message file structure (existing format):
interface MessageFile {
  messages: Array<{
    id: string;
    timestamp: string; // ISO date
    from: "human" | "agent";
    to: "human" | "agent";
    message: string;
    status: "unread" | "read" | "answered";
  }>;
}
```

#### Situational Awareness Logic
```typescript
// From milestone-control.sh lines 195-219
interface SituationalAwareness {
  analyzeCurrentSituation(phase: string, lastUpdated: string, currentTask: number):
    "stuck_long" | "stuck_medium" | "git_conflict" | "analysis_slow" | "normal";

  // Detection patterns:
  // - stuck_long: >45 minutes since last update
  // - stuck_medium: >20 minutes in task_execution phase
  // - git_conflict: git status shows conflicts or merge issues
  // - analysis_slow: >10 minutes in pre_review phase
}
```

#### Git Workflow Management
```typescript
// From milestone-guide.sh lines 873-1072
interface GitWorkflowManager {
  setupTaskBranch(milestoneId: string, taskNumber: number): Promise<void>;
  // Creates: milestone/{milestone-id}/task-{nn} branch structure

  commitTaskWork(taskNumber: number, milestoneId: string): Promise<void>;
  // Commit message format: "feat(task-{n}): Complete task {n} implementation"

  mergeTaskToMilestone(taskNumber: number, milestoneId: string): Promise<void>;
  // Uses: git merge --squash for clean history

  validateGitState(): Promise<GitValidationResult>;
  // Checks: uncommitted changes, current branch, conflicts
}
```

---

### Confidence Scoring Logic
```typescript
// From milestone-guide.sh lines 590-627
interface ConfidenceScoring {
  extractConfidenceScore(analysisFile: string): number;
  // Regex: /confidence.*score.*([0-9]+)/i
  // Threshold: ≥9 for autonomous execution

  evaluateImplementationReadiness(milestone: Milestone): ConfidenceScore;
  // Factors: task clarity, technical feasibility, resource availability
}
```

### Work Log Validation Patterns
```typescript
// From milestone-guide.sh lines 934-999
interface WorkLogValidator {
  validateWorkLogs(taskNumber: number, milestoneId: string): Promise<boolean>;

  // Accepted completion patterns:
  // - "### Task {n}: COMPLETED"
  // - "Task {n} completed"
  // - "Task {n}: done"
  // - "✅ Task {n} finished"
  // - Any task header followed by completion indicator within 5 lines
}
```

## 🤖 CI Pipeline

### Build Pipeline
```yaml
name: Milestone Automation Build
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm run lint
      - run: pnpm run type-check
      - run: pnpm run test:unit
      - run: pnpm run test:integration
      - run: pnpm run build

  compatibility:
    runs-on: ubuntu-latest
    needs: test
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm run build
      - run: ./scripts/test-cli-compatibility.sh
      - run: ./scripts/test-performance-benchmarks.sh

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm audit
      - run: pnpm run security-scan
```

### Quality Gates
- **Code Coverage**: ≥85% required for merge
- **Type Safety**: 0 TypeScript errors allowed
- **Linting**: 0 ESLint errors/warnings allowed
- **Security**: No high/critical vulnerabilities
- **Performance**: CLI startup benchmarks must pass
- **Compatibility**: All bash script compatibility tests must pass

---

## 🔨 Task Breakdown

### Phase 1: Foundation & Domain Layer (Week 1)

| # | Branch | Task | Implementation Guide | Estimated Time |
|---|--------|------|---------------------|----------------|
| 1 | `m4/setup` | Project setup with TypeScript, testing, and build configuration | Setup package.json, tsconfig.json, jest.config.js in packages/milestone-automation/. Configure build scripts and dev dependencies. | 4 hours |
| 2 | `m4/domain-entities` | Implement core domain entities (Milestone, Task, ExecutionState, WorkLog) | Create entities with business logic only. No external dependencies. Include validation and state transition rules. | 6 hours |
| 3 | `m4/value-objects` | Create value objects (MilestoneId, Phase, ConfidenceScore, TaskStatus) | Implement immutable value objects with validation. ConfidenceScore.isHighConfidence() returns score ≥ 9. | 4 hours |
| 4 | `m4/domain-services` | Implement domain services (AnalysisService, ValidationService) | Extract confidence scoring logic from bash script. Implement spec-lint validation patterns. | 8 hours |
| 5 | `m4/repositories` | Define repository interfaces and contracts | Create interfaces only. No implementations yet. Define contracts for state, milestone, and message persistence. | 3 hours |

### Phase 2: Application Layer (Week 2)

| # | Branch | Task | Implementation Guide | Estimated Time |
|---|--------|------|---------------------|----------------|
| 6 | `m4/use-cases-prereview` | Implement PreReviewUseCase with confidence scoring logic | Replicate milestone-guide.sh lines 577-627. Extract confidence from analysis files. Handle autonomous vs human decision paths. | 8 hours |
| 7 | `m4/use-cases-execution` | Implement ExecuteTaskUseCase with git workflow management | Most complex task. Replicate milestone-guide.sh task execution flow (lines 796-932). Include branch management, work log validation, commit patterns. | 16 hours |
| 8 | `m4/use-cases-finalization` | Implement FinalizeUseCase with acceptance testing | Replicate milestone-guide.sh finalization phase (lines 1075-1200). Include acceptance test execution and cleanup. | 8 hours |
| 9 | `m4/use-cases-recovery` | Implement RecoveryUseCase with error handling scenarios | Replicate milestone-guide.sh recovery logic (lines 1300-1350). Handle stuck detection and manual recovery guidance. | 10 hours |
| 10 | `m4/orchestrator` | Create MilestoneOrchestrator for workflow coordination | Coordinate use cases. Replicate main phase routing logic from milestone-guide.sh lines 175-184. | 4 hours |

### Phase 3: Infrastructure & CLI (Week 3)

| # | Branch | Task | Implementation Guide | Estimated Time |
|---|--------|------|---------------------|----------------|
| 11 | `m4/infrastructure-repos` | Implement file-based repositories (JSON state, milestone files) | Implement StateAdapter for legacy compatibility. Use existing JSON formats. Handle file I/O with proper error handling. | 8 hours |
| 12 | `m4/infrastructure-services` | Implement external services (Git, SpecLint, FileSystem) | Wrap shell commands for git operations. Integrate with existing spec-lint.mjs. Maintain exact command patterns from bash scripts. | 8 hours |
| 13 | `m4/cli-controllers` | Create CLI controllers with argument parsing and presentation | Use Commander.js to replicate exact CLI interface. Maintain identical help text and error messages. Handle --autonomous flag. | 8 hours |
| 14 | `m4/api-foundation` | Implement basic REST API with Express.js foundation | Create REST endpoints for milestone operations. Use existing use cases. Provide JSON responses for state and operations. | 6 hours |
| 15 | `m4/migration-testing` | End-to-end testing and bash script compatibility validation | Critical task. Create comprehensive test suite comparing bash vs TypeScript behavior. Test all 14 acceptance test scenarios. | 12 hours |

---

## 🧪 Acceptance Tests

### CLI Compatibility Tests
```bash
# Test 1: Basic milestone execution
./milestone-guide.js M1 augment
# Expected: Identical behavior to ./milestone-guide.sh M1 augment

# Test 2: Autonomous mode
./milestone-guide.js M1 augment --autonomous
# Expected: Same autonomous decision-making process

# Test 3: Control operations
./milestone-control.js M1 status
./milestone-control.js M1 message "Test message"
# Expected: Identical functionality to bash versions
```

### State Management Compatibility Tests
```bash
# Test 4: State file format compatibility
# 1. Create state with bash: ./milestone-guide.sh M1 augment
# 2. Read state with TypeScript: ./milestone-guide.js M1 status
# 3. Verify identical state interpretation

# Test 5: Message system compatibility
# 1. Send message with bash: ./milestone-control.sh M1 message "test"
# 2. Read messages with TypeScript: ./milestone-control.js M1 messages
# 3. Verify message format and status handling

# Test 6: Cross-system state transitions
# 1. Start with bash script (phase: not_started → analysis_pending)
# 2. Continue with TypeScript (phase: analysis_pending → instructions_ready)
# 3. Verify seamless phase transitions and state integrity
```

### Behavioral Compatibility Tests
```bash
# Test 7: Situational awareness matching
# Setup: Create stuck scenario (task running >45 minutes)
# Bash: ./milestone-control.sh M1 status
# TypeScript: ./milestone-control.js M1 status
# Expected: Identical "stuck_long" detection and guidance

# Test 8: Git workflow compatibility
# 1. Start task with bash script (creates milestone/m1/task-01 branch)
# 2. Complete task with TypeScript (merge and cleanup)
# 3. Verify identical git history and branch management

# Test 9: Work log validation matching
# Setup: Create work log with various completion patterns
# Bash: Validate with milestone-guide.sh work log checker
# TypeScript: Validate with equivalent TypeScript logic
# Expected: Identical validation results for all patterns
```

### Performance Benchmarks
```bash
# Test 10: CLI startup time
time ./milestone-guide.sh M1 --help    # Baseline: ~0.5s
time ./milestone-guide.js M1 --help    # Target: ≤3s (6x acceptable)

# Test 11: Memory usage comparison
# Bash: Monitor RSS during milestone execution
# TypeScript: Monitor Node.js heap and RSS
# Target: ≤150MB for typical operations

# Test 12: File I/O performance
# Compare state file read/write operations
# Compare message file operations
# Target: No significant regression in file operations
```

### API Foundation Tests
```bash
# Test 13: REST API basic operations
curl -X GET http://localhost:3000/api/milestones/M1/status
curl -X POST http://localhost:3000/api/milestones/M1/pre-review
# Expected: Valid JSON responses with milestone data

# Test 14: API state synchronization
# 1. Update state via CLI
# 2. Query state via API
# 3. Verify consistent state representation
```

---

## 🔧 Technical Requirements

### Dependencies
- **Runtime**: Node.js ≥18.0.0
- **Language**: TypeScript ≥5.0.0 with strict mode
- **Testing**: Jest with ts-jest for unit/integration tests
- **CLI**: Commander.js for argument parsing
- **API**: Express.js for REST endpoints
- **Validation**: Zod for runtime type validation
- **Logging**: Winston for structured logging

### Architecture Constraints
- **Pure Domain Layer**: No external dependencies in domain entities
- **Dependency Injection**: Use dependency injection for all services
- **Error Handling**: Custom error types with proper error boundaries
- **Configuration**: Environment-based config with validation
- **Backward Compatibility**: Existing JSON state files must work

### Performance Requirements
- **CLI Startup**: ≤3 seconds for simple commands (relaxed from 2s due to Node.js overhead)
- **Memory Usage**: ≤150MB for typical milestone operations (increased for TypeScript runtime)
- **File Operations**: Maintain current file I/O performance
- **Git Operations**: No performance regression for git workflows

### Implementation Examples

#### Domain Entity Implementation
```typescript
// Example: Milestone entity with business logic
export class Milestone {
  constructor(
    private readonly id: MilestoneId,
    private readonly tasks: Task[],
    private phase: Phase,
    private confidenceScore?: ConfidenceScore
  ) {}

  canProceedToExecution(): boolean {
    return this.confidenceScore?.isHighConfidence() ?? false;
  }

  updatePhase(newPhase: Phase): void {
    // Business rule: Can only move forward in phases
    if (!this.phase.canTransitionTo(newPhase)) {
      throw new InvalidPhaseTransitionError(this.phase, newPhase);
    }
    this.phase = newPhase;
  }

  getCurrentTask(): Task | null {
    return this.tasks.find(task => task.isInProgress()) ?? null;
  }
}
```

#### State Adapter Implementation
```typescript
// Example: Legacy state compatibility
export class StateAdapter {
  convertLegacyToNew(legacyState: any): ExecutionState {
    return new ExecutionState(
      new MilestoneId(legacyState.milestone_id),
      Phase.fromString(legacyState.current_phase),
      legacyState.current_task,
      legacyState.total_tasks,
      new Date(legacyState.last_updated),
      legacyState.phase_history?.map(h => new PhaseHistoryEntry(
        Phase.fromString(h.phase),
        h.task,
        new Date(h.timestamp)
      )) ?? []
    );
  }

  convertNewToLegacy(state: ExecutionState): any {
    return {
      milestone_id: state.milestoneId.value,
      current_phase: state.currentPhase.toString(),
      current_task: state.currentTask,
      total_tasks: state.totalTasks,
      last_updated: state.lastUpdated.toISOString(),
      phase_history: state.phaseHistory.map(h => ({
        phase: h.phase.toString(),
        task: h.task,
        timestamp: h.timestamp.toISOString()
      }))
    };
  }
}
```

#### CLI Controller Implementation
```typescript
// Example: Maintaining exact CLI compatibility
export class MilestoneGuideController {
  async run(args: string[]): Promise<void> {
    // Parse arguments exactly like bash script
    const milestoneId = args[0];
    const agentType = args[1] || 'augment';
    const isAutonomous = args.includes('--autonomous');

    if (!milestoneId) {
      this.showUsage();
      process.exit(1);
    }

    // Delegate to use cases with same logic flow as bash
    const request = new PreReviewRequest(
      new MilestoneId(milestoneId),
      agentType,
      isAutonomous
    );

    const response = await this.preReviewUseCase.execute(request);

    // Output formatting matches bash script exactly
    this.formatOutput(response);
  }

  private showUsage(): void {
    console.log(`Usage: ${process.argv[1]} <milestone-id> [agent-type] [--autonomous]`);
    console.log('Example: milestone-guide.js M1.2 augment');
    console.log('Example: milestone-guide.js M1.2 augment --autonomous');
  }
}
```

---

## 🚨 Risk Assessment

### High Risks
- **Complexity**: Large architectural change with many moving parts
- **Compatibility**: Ensuring functional parity with bash scripts (exact byte-for-byte compatibility not required)
- **Performance**: TypeScript overhead vs bash script speed (acceptable 6x slowdown)
- **Bash Logic Migration**: Complex situational awareness and error recovery logic needs careful analysis

### Mitigation Strategies
- **Incremental Migration**: Phase-by-phase implementation with testing
- **Compatibility Testing**: Extensive test suite comparing bash vs TypeScript functional behavior
- **Performance Monitoring**: Benchmark critical operations throughout development
- **Bash Script Analysis**: Comprehensive documentation of all bash script behaviors before migration
- **Prototype Critical Components**: Build proof-of-concept for complex use cases (ExecuteTaskUseCase, RecoveryUseCase)

### Rollback Plan
- **Bash Scripts Preserved**: Keep original scripts as fallback
- **Feature Flags**: Environment variable to switch between implementations
- **State Compatibility**: Ensure state files work with both versions

---

## 📈 Implementation Strategy

### Development Approach
1. **Domain-First**: Start with pure business logic, no external dependencies
2. **Test-Driven**: Write tests before implementation for critical use cases
3. **Incremental**: Build and test each layer before moving to the next
4. **Compatibility-First**: Ensure CLI compatibility at every step

### Quality Gates
- **Code Review**: All code reviewed for DDD principles adherence
- **Test Coverage**: ≥85% coverage required for merge
- **Performance**: Benchmark tests must pass before merge
- **Documentation**: Architecture decisions documented in ADRs

---

## 🎯 Definition of Done

- [ ] All 15 tasks completed and merged to main branch
- [ ] CLI commands have 100% functional parity with bash scripts
- [ ] Test suite passes with ≥85% coverage
- [ ] Performance benchmarks meet requirements
- [ ] API foundation endpoints functional
- [ ] Documentation complete and reviewed
- [ ] Migration guide validated with real milestone execution
- [ ] Rollback strategy tested and documented

**Final Validation**: Successfully execute a real milestone (M1 or M2) using the new TypeScript system with identical results to the bash version.

---

## 🛠️ Technical Architecture

### Domain Layer Structure
```typescript
// Core Entities
class Milestone {
  constructor(
    private id: MilestoneId,
    private tasks: Task[],
    private phase: Phase,
    private confidenceScore?: ConfidenceScore
  ) {}

  canProceedToExecution(): boolean {
    return this.confidenceScore?.isHighConfidence() ?? false;
  }
}

class Task {
  constructor(
    private id: TaskId,
    private status: TaskStatus,
    private branch: string,
    private workLog?: WorkLog
  ) {}
}

// Value Objects
class ConfidenceScore {
  constructor(private value: number) {
    if (value < 1 || value > 10) {
      throw new Error('Confidence score must be between 1 and 10');
    }
  }

  isHighConfidence(): boolean {
    return this.value >= 9; // Matches bash script threshold
  }
}
```

### Application Layer Structure
```typescript
// Use Cases
class PreReviewUseCase {
  constructor(
    private milestoneRepo: MilestoneRepository,
    private analysisService: AnalysisService,
    private validationService: ValidationService
  ) {}

  async execute(request: PreReviewRequest): Promise<PreReviewResponse> {
    const milestone = await this.milestoneRepo.findById(request.milestoneId);
    const quickAnalysis = await this.analysisService.performQuickAnalysis(milestone);
    const detailedAnalysis = await this.analysisService.performDetailedAnalysis(milestone);

    if (request.isAutonomous) {
      return this.handleAutonomousDecision(quickAnalysis, detailedAnalysis);
    }

    return this.handleHumanDecision(quickAnalysis, detailedAnalysis);
  }
}
```

### Infrastructure Layer Structure
```typescript
// Repository Implementations
class FileMilestoneRepository implements MilestoneRepository {
  async findById(id: MilestoneId): Promise<Milestone> {
    const filePath = `docs/tech-specs/milestones/milestone-${id.value}.mdx`;
    const content = await this.fileSystem.readFile(filePath);
    return this.milestoneParser.parse(content);
  }
}

// External Service Adapters
class ShellGitService implements GitService {
  async createBranch(name: string): Promise<void> {
    await this.shell.exec(`git checkout -b ${name}`);
  }
}
```

---

## 🔄 Migration Compatibility

### State File Compatibility
```typescript
// Ensure backward compatibility with existing JSON state files
interface LegacyState {
  milestone_id: string;
  current_phase: string;
  current_task: number;
  total_tasks: number;
  // ... other legacy fields
}

class StateAdapter {
  convertLegacyToNew(legacy: LegacyState): ExecutionState {
    return new ExecutionState(
      new MilestoneId(legacy.milestone_id),
      Phase.fromString(legacy.current_phase),
      legacy.current_task,
      legacy.total_tasks
    );
  }
}
```

### CLI Argument Compatibility
```typescript
// Maintain exact same CLI interface
// ./milestone-guide.js M1 augment --autonomous
class MilestoneGuideController {
  async run(args: string[]): Promise<void> {
    const milestoneId = args[0];
    const agentType = args[1] || 'augment';
    const isAutonomous = args.includes('--autonomous');

    // Delegate to use cases...
  }
}
```

---

## 📊 Success Metrics

### Maintainability Improvements
- **Cyclomatic Complexity**: Reduce from bash script complexity to <10 per function
- **Code Duplication**: Eliminate duplicate logic through proper abstraction
- **Separation of Concerns**: Clear boundaries between domain, application, and infrastructure

### Developer Experience Improvements
- **Type Safety**: 100% TypeScript coverage with strict mode
- **IDE Support**: Full IntelliSense, refactoring, and debugging capabilities
- **Testing**: Unit tests for all business logic, integration tests for workflows
- **Documentation**: Auto-generated API docs, architecture decision records

### Extensibility Improvements
- **New Features**: Add new milestone phases without modifying existing code
- **Multiple Interfaces**: Support CLI, API, and future web interfaces
- **Plugin Architecture**: Support for custom analysis providers and validators
- **Configuration**: Environment-based configuration for different deployment scenarios

---

## 🔍 Quality Assurance

### Testing Strategy
```typescript
// Unit Tests
describe('ConfidenceScore', () => {
  it('should identify high confidence correctly', () => {
    const highScore = new ConfidenceScore(9);
    expect(highScore.isHighConfidence()).toBe(true);
  });
});

// Integration Tests
describe('PreReviewUseCase', () => {
  it('should proceed when confidence is high', async () => {
    // Test full workflow with mocked dependencies
  });
});

// End-to-End Tests
describe('CLI Compatibility', () => {
  it('should match bash script behavior', async () => {
    // Compare outputs between bash and TypeScript versions
  });
});
```

### Performance Benchmarks
```typescript
// Performance Tests
describe('Performance', () => {
  it('should start CLI within 2 seconds', async () => {
    const start = Date.now();
    await runCLI(['M1', '--help']);
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(2000);
  });
});
```

---

## 🚀 Future Roadmap

### Phase 4: Web Interface (Future)
- React-based dashboard for milestone management
- Real-time progress tracking
- Visual workflow representation
- Team collaboration features

### Phase 5: Advanced Features (Future)
- AI-powered milestone analysis
- Automated dependency detection
- Integration with project management tools
- Advanced reporting and analytics

---

## ✅ Acceptance Criteria Summary

### Must Have (MVP)
- [ ] **Functional Parity**: Equivalent functionality with existing bash scripts (not byte-identical)
- [ ] **Type Safety**: Full TypeScript with strict mode
- [ ] **Test Coverage**: ≥85% unit test coverage
- [ ] **Performance**: CLI startup ≤3 seconds
- [ ] **Documentation**: Complete architecture and API docs

### Should Have
- [ ] **API Foundation**: Basic REST endpoints for milestone operations
- [ ] **Error Handling**: Comprehensive error types and logging
- [ ] **Configuration**: Environment-based configuration system
- [ ] **Migration Guide**: Step-by-step migration documentation

### Could Have
- [ ] **Web Dashboard**: Basic web interface for milestone management
- [ ] **Webhook Support**: Integration with external systems
- [ ] **Plugin Architecture**: Support for custom extensions
- [ ] **Advanced Analytics**: Detailed reporting and metrics

---

## 📋 Implementation Checklist

### Pre-Implementation
- [ ] Review existing bash scripts for all functionality
- [ ] Document all bash script behaviors, edge cases, and error handling
- [ ] Identify all external dependencies and integrations
- [ ] Set up development environment with TypeScript tooling
- [ ] Create comprehensive test plan
- [ ] Build prototypes for complex use cases (ExecuteTaskUseCase, RecoveryUseCase)

### During Implementation
- [ ] Follow TDD approach for critical business logic
- [ ] Maintain backward compatibility at each step
- [ ] Document architectural decisions in ADRs
- [ ] Regular performance benchmarking

### Post-Implementation
- [ ] Comprehensive testing with real milestones
- [ ] Performance validation and optimization
- [ ] Documentation review and updates
- [ ] Migration guide validation

**Ready for Agent Execution**: This milestone is designed to be executed by a software agent using the current `milestone-guide.sh` script, providing a real-world test of the automation system while building its own replacement.

---

## 🧪 Comprehensive Testing Strategy

### Unit Testing Approach
```typescript
// Example: Domain entity tests
describe('Milestone', () => {
  it('should allow valid phase transitions', () => {
    const milestone = new Milestone(id, tasks, Phase.NOT_STARTED);
    milestone.updatePhase(Phase.ANALYSIS_PENDING);
    expect(milestone.currentPhase).toBe(Phase.ANALYSIS_PENDING);
  });

  it('should reject invalid phase transitions', () => {
    const milestone = new Milestone(id, tasks, Phase.NOT_STARTED);
    expect(() => milestone.updatePhase(Phase.MILESTONE_DONE))
      .toThrow(InvalidPhaseTransitionError);
  });
});

// Example: Use case tests with mocked dependencies
describe('PreReviewUseCase', () => {
  it('should proceed when confidence is high', async () => {
    const mockAnalysisService = {
      extractConfidenceScore: jest.fn().mockResolvedValue(9)
    };

    const useCase = new PreReviewUseCase(mockAnalysisService);
    const result = await useCase.execute(request);

    expect(result.shouldProceed).toBe(true);
  });
});
```

### Integration Testing Strategy
```typescript
// Example: State persistence integration test
describe('State Management Integration', () => {
  it('should maintain compatibility with legacy state files', async () => {
    // 1. Create state with legacy format
    const legacyState = createLegacyStateFile();

    // 2. Read with new TypeScript system
    const stateRepo = new FileMilestoneRepository();
    const state = await stateRepo.loadState(milestoneId);

    // 3. Verify correct interpretation
    expect(state.currentPhase).toBe(Phase.TASK_EXECUTION);
    expect(state.currentTask).toBe(3);
  });
});
```

### Behavioral Compatibility Testing
```bash
#!/bin/bash
# scripts/test-cli-compatibility.sh

# Test identical CLI behavior
test_cli_compatibility() {
  local milestone_id="TEST_M1"

  # Test 1: Help output comparison
  bash_help=$(./milestone-guide.sh --help 2>&1)
  ts_help=$(./milestone-guide.js --help 2>&1)

  if [[ "$bash_help" != "$ts_help" ]]; then
    echo "❌ Help output differs"
    return 1
  fi

  # Test 2: State file format compatibility
  ./milestone-guide.sh $milestone_id augment  # Create state with bash
  ./milestone-guide.js $milestone_id status   # Read state with TypeScript

  # Verify no errors and identical output format
}
```

---

# 🛠️ Bash-to-TypeScript Migration Mapping (NEW SECTION)

| Bash Script Function/Section                | TypeScript Module/Responsibility                | Notes |
|---------------------------------------------|------------------------------------------------|-------|
| State management (lines 334-351)            | domain/entities/ExecutionState.ts, infrastructure/adapters/StateAdapter.ts | Ensure all state transitions and JSON compatibility are covered |
| Human-agent messaging (lines 354-433)       | domain/services/MessageService.ts, infrastructure/adapters/MessageAdapter.ts | Maintain message file format and status handling |
| Situational awareness (lines 195-219)       | application/use-cases/RecoveryUseCase.ts, domain/services/SituationalAwarenessService.ts | Implement all detection patterns (stuck, conflict, etc.) |
| Git workflow (lines 873-1072)               | infrastructure/services/ShellGitService.ts, application/use-cases/ExecuteTaskUseCase.ts | Replicate branch, commit, merge, and validation logic |
| Confidence scoring (lines 590-627)          | domain/value-objects/ConfidenceScore.ts, domain/services/AnalysisService.ts | Regex extraction and threshold logic |
| Work log validation (lines 934-999)         | domain/services/WorkLogValidator.ts             | Implement all completion patterns |

---
# 🎯 Clarified Acceptance Criteria for Complex Tasks (NEW/UPDATED)

- **ExecuteTaskUseCase**: Must replicate all git operations (branching, committing, merging) as in bash, including error handling for conflicts and validation of branch structure. Provide TypeScript code examples for each operation in the implementation guide.
- **State Compatibility**: TypeScript system must read, write, and update JSON state files with 100% compatibility. Add test cases for legacy-to-new and new-to-legacy state transitions.
- **RecoveryUseCase**: Must detect and handle all stuck/slow/conflict scenarios as in bash. Add a table of detection patterns and required agent responses.
- **Performance**: CLI startup time must be ≤3s (measured with `time`), and memory usage ≤150MB. Add a performance test script and document baseline results.
- **Error Handling**: All error conditions from bash must be mapped to custom error types in TypeScript, with clear logging and user guidance. Add a table mapping bash error messages to TypeScript error classes.

---
# 🧪 Integration Test Scenarios (NEW SECTION)

- **State File Compatibility**: Test reading/writing state files created by both bash and TypeScript systems, including edge cases (paused, incomplete, corrupted files).
- **Git Workflow**: Test branch creation, commit, and merge operations for all milestone phases, including error and recovery paths.
- **Message System**: Test sending, receiving, and status updates for messages between human and agent, including unread/read/answered transitions.
- **Recovery Scenarios**: Simulate stuck, slow, and conflict situations and verify correct detection and agent response.
- **Performance Baseline**: Record and document CLI startup and memory usage for both bash and TypeScript systems.

---
# 📊 Performance and Error Handling Requirements (UPDATED)

- **Performance**: Add a table of baseline measurements and targets for CLI startup, memory usage, and file I/O. Document how to run and interpret performance tests.
- **Error Handling**: Add a mapping table from bash error messages to TypeScript error classes, and require that all errors are logged with context and user guidance.

---
# 📝 Implementation Guidance (ENHANCED)

- For each complex use case (ExecuteTask, Recovery, StateAdapter), provide a TypeScript code snippet or pseudocode in the implementation guide.
- For git workflow, include examples for branch creation, commit, merge, and conflict resolution.
- For state compatibility, include a test matrix for all legacy and new state fields and transitions.
- For recovery, list all detection patterns and required agent actions in a table.

---
# ✅ Updated Definition of Done (ENHANCED)

- [ ] All bash-to-TypeScript mappings are implemented and tested
- [ ] All complex acceptance criteria are covered with concrete examples and test cases
- [ ] All integration test scenarios are automated and pass
- [ ] Performance and error handling requirements are met and documented
- [ ] Migration guide includes a section on legacy compatibility and test results

---

## 🧩 Example: TypeScript Code for Branch Creation (NEW)

```typescript
// infrastructure/services/ShellGitService.ts
export class ShellGitService {
  async createBranch(milestoneId: string, taskNumber: number): Promise<void> {
    const branch = `milestone/${milestoneId}/task-${String(taskNumber).padStart(2, '0')}`;
    await this.shell.exec(`git checkout -b ${branch}`);
  }
}
```

## 🧪 Sample Integration Test Case (NEW)

```typescript
describe('State File Compatibility', () => {
  it('should read legacy state and update with new system', async () => {
    // 1. Create legacy state file
    const legacyState = {
      milestone_id: 'M4',
      current_phase: 'task_execution',
      current_task: 2,
      total_tasks: 5,
      agent_type: 'augment',
      started_at: '2025-06-01T10:00:00Z',
      last_updated: '2025-06-01T12:00:00Z',
      phase_history: []
    };
    await writeFile('state/M4/current-state.json', JSON.stringify(legacyState));
    // 2. Load with TypeScript repo
    const repo = new FileMilestoneRepository();
    const state = await repo.loadState('M4');
    expect(state.currentPhase).toBe('task_execution');
    // 3. Update and verify
    state.updatePhase('milestone_done');
    await repo.saveState(state);
    const updated = await repo.loadState('M4');
    expect(updated.currentPhase).toBe('milestone_done');
  });
});
```

## 📊 Performance Baseline Table (NEW)

| Metric                | Bash Baseline | TypeScript Target | Notes                  |
|----------------------|--------------|------------------|------------------------|
| CLI Startup Time     | ~0.5s        | ≤3s              | Measured with `time`   |
| Memory Usage         | ~30MB        | ≤150MB           | RSS, typical operation |
| State File I/O       | <50ms        | <100ms           | Read/write, avg.       |

## 🛑 Error Mapping Table

| Bash Error Message/Scenario | TypeScript Error Class | Logging/Guidance Provided |
|----------------------------|------------------------|-------------------------|
| "State file corrupt"                | StateFileCorruptError         | Log error, suggest reset            |
| "Git conflict detected"             | GitConflictError              | Log error, show conflict resolution |
| "Missing milestone file"            | MilestoneFileNotFoundError    | Log error, list available files     |
| "Work log incomplete"               | WorkLogIncompleteError        | Log error, prompt for update        |
| "Spec-lint errors found"            | SpecLintValidationError       | Log error, show lint output         |

---

## 🔄 Document History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.3.0 | 2025-06-19 | cursor | Added TypeScript examples, test cases, tables |
| 1.2.0 | 2025-06-18 | augment | Enhanced implementation guidance |
| 1.1.0 | 2025-06-16 | augment | Updated requirements and risk analysis |
| 1.0.0 | 2024-12-19 | nitishMehrotra | Initial DDD architecture design |

### Change Log
- **v1.3.0** (2025-06-19, cursor): Added concrete TypeScript code example for branch creation, a sample integration test case, a performance baseline table, an error mapping table, and updated the document history to reflect these changes.
- **v1.2.0**: Major specification enhancement to address implementation readiness concerns
  - Added detailed bash script analysis with function mappings and behavior documentation
  - Included comprehensive implementation examples for domain entities, state adapters, and CLI controllers
  - Enhanced task breakdown with specific implementation guidance and bash script line references
  - Added extensive testing strategy with unit, integration, and behavioral compatibility tests
  - Documented critical functions: state management, message system, situational awareness, git workflows
  - Added performance benchmarking details and acceptance test scenarios
  - Included TypeScript code examples for complex migration patterns
- **v1.1.0**: Updated milestone based on implementation feasibility analysis
  - Relaxed CLI startup time from 2s to 3s (accounting for Node.js overhead)
  - Increased memory usage limit from 100MB to 150MB
  - Clarified compatibility requirements (functional parity vs byte-identical)
  - Increased time estimates for complex tasks (ExecuteTaskUseCase, RecoveryUseCase, migration testing)
  - Added bash script analysis and prototyping requirements
  - Updated performance benchmarks to allow 6x slowdown vs bash
- **v1.0.0**: Created comprehensive milestone specification for TypeScript DDD migration
  - Defined 15 tasks across 3 phases for systematic implementation
  - Established architecture with domain/application/infrastructure separation
  - Specified compatibility requirements and performance benchmarks
  - Added comprehensive testing strategy and quality gates
  - Included migration path and rollback strategy

---
